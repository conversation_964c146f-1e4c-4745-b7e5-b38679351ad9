{"info": {"name": "Tests Imprimer Facture et Devis", "description": "Collection de tests Postman pour l'impression des factures et devis", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:5000/api", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "factureId", "value": "", "type": "string"}, {"key": "devisId", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.collectionVariables.set('token', jsonData.token);", "});", "", "pm.test(\"User role is admin\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.user.role).to.eql('admin');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Login <PERSON> Vendeur", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has token\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('token');", "    pm.collectionVariables.set('token', jsonData.token);", "});", "", "pm.test(\"User role is vendeur\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.user.role).to.eql('vendeur');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"wajdi<PERSON><PERSON><PERSON><EMAIL>\",\n  \"password\": \"112233\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "Verify <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Token is valid\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.valid).to.be.true;", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/auth/verify-token", "host": ["{{baseUrl}}"], "path": ["auth", "verify-token"]}}}]}, {"name": "Setup Data", "item": [{"name": "Get All Factures", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has factures array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    if (jsonData.length > 0) {", "        pm.collectionVariables.set('factureId', jsonData[0]._id);", "        console.log('Set factureId to:', jsonData[0]._id);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/factures", "host": ["{{baseUrl}}"], "path": ["factures"]}}}, {"name": "Get All Devis", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has devis array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array');", "    if (jsonData.length > 0) {", "        pm.collectionVariables.set('devisId', jsonData[0]._id);", "        console.log('Set devisId to:', jsonData[0]._id);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/devis", "host": ["{{baseUrl}}"], "path": ["devis"]}}}]}, {"name": "Facture Printing Tests", "item": [{"name": "Download Facture PDF", "event": [{"listen": "prerequest", "script": {"exec": ["// Check if factureId is set", "if (!pm.collectionVariables.get('factureId')) {", "    console.log('factureId not set, please run \"Get All Factures\" first');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is PDF\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/pdf');", "});", "", "pm.test(\"Content-Disposition header is attachment\", function () {", "    var contentDisposition = pm.response.headers.get('Content-Disposition');", "    pm.expect(contentDisposition).to.include('attachment');", "    pm.expect(contentDisposition).to.include('facture-');", "});", "", "pm.test(\"PDF content is not empty\", function () {", "    pm.expect(pm.response.stream).to.have.property('length');", "    pm.expect(pm.response.stream.length).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/factures/{{factureId}}/pdf", "host": ["{{baseUrl}}"], "path": ["factures", "{{factureId}}", "pdf"]}}}, {"name": "Print Facture PDF (Inline)", "event": [{"listen": "prerequest", "script": {"exec": ["// Check if factureId is set", "if (!pm.collectionVariables.get('factureId')) {", "    console.log('factureId not set, please run \"Get All Factures\" first');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is PDF\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/pdf');", "});", "", "pm.test(\"Content-Disposition header is inline\", function () {", "    var contentDisposition = pm.response.headers.get('Content-Disposition');", "    pm.expect(contentDisposition).to.include('inline');", "    pm.expect(contentDisposition).to.include('facture-');", "});", "", "pm.test(\"PDF content is not empty\", function () {", "    pm.expect(pm.response.stream).to.have.property('length');", "    pm.expect(pm.response.stream.length).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/factures/{{factureId}}/print", "host": ["{{baseUrl}}"], "path": ["factures", "{{factureId}}", "print"]}}}, {"name": "Test Invalid Facture ID - PDF", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Error message for invalid ID\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.error).to.include('not found');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/factures/507f1f77bcf86cd799439011/pdf", "host": ["{{baseUrl}}"], "path": ["factures", "507f1f77bcf86cd799439011", "pdf"]}}}, {"name": "Test Unauthorized Access - No Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Error message for unauthorized access\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.error).to.include('Token');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/factures/{{factureId}}/pdf", "host": ["{{baseUrl}}"], "path": ["factures", "{{factureId}}", "pdf"]}}}]}, {"name": "Devis Printing Tests", "item": [{"name": "Download Devis PDF", "event": [{"listen": "prerequest", "script": {"exec": ["// Check if devisId is set", "if (!pm.collectionVariables.get('devisId')) {", "    console.log('devisId not set, please run \"Get All Devis\" first');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is PDF\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/pdf');", "});", "", "pm.test(\"Content-Disposition header is attachment\", function () {", "    var contentDisposition = pm.response.headers.get('Content-Disposition');", "    pm.expect(contentDisposition).to.include('attachment');", "    pm.expect(contentDisposition).to.include('devis-');", "});", "", "pm.test(\"PDF content is not empty\", function () {", "    pm.expect(pm.response.stream).to.have.property('length');", "    pm.expect(pm.response.stream.length).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/devis/{{devisId}}/pdf", "host": ["{{baseUrl}}"], "path": ["devis", "{{devisId}}", "pdf"]}}}, {"name": "Print Devis PDF (Inline)", "event": [{"listen": "prerequest", "script": {"exec": ["// Check if devisId is set", "if (!pm.collectionVariables.get('devisId')) {", "    console.log('devisId not set, please run \"Get All Devis\" first');", "}"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is PDF\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/pdf');", "});", "", "pm.test(\"Content-Disposition header is inline\", function () {", "    var contentDisposition = pm.response.headers.get('Content-Disposition');", "    pm.expect(contentDisposition).to.include('inline');", "    pm.expect(contentDisposition).to.include('devis-');", "});", "", "pm.test(\"PDF content is not empty\", function () {", "    pm.expect(pm.response.stream).to.have.property('length');", "    pm.expect(pm.response.stream.length).to.be.greaterThan(0);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/devis/{{devisId}}/print", "host": ["{{baseUrl}}"], "path": ["devis", "{{devisId}}", "print"]}}}, {"name": "Test Invalid Devis ID - PDF", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test(\"Error message for invalid ID\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.error).to.include('not found');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/devis/507f1f77bcf86cd799439011/pdf", "host": ["{{baseUrl}}"], "path": ["devis", "507f1f77bcf86cd799439011", "pdf"]}}}, {"name": "Test Unauthorized Access - Devis No Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {", "    pm.response.to.have.status(401);", "});", "", "pm.test(\"Error message for unauthorized access\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.error).to.include('Token');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/devis/{{devisId}}/pdf", "host": ["{{baseUrl}}"], "path": ["devis", "{{devisId}}", "pdf"]}}}]}, {"name": "Performance Tests", "item": [{"name": "Facture PDF Generation Performance", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test(\"PDF is generated successfully\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/pdf');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/factures/{{factureId}}/pdf", "host": ["{{baseUrl}}"], "path": ["factures", "{{factureId}}", "pdf"]}}}, {"name": "Devis PDF Generation Performance", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test(\"PDF is generated successfully\", function () {", "    pm.expect(pm.response.headers.get('Content-Type')).to.include('application/pdf');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/devis/{{devisId}}/pdf", "host": ["{{baseUrl}}"], "path": ["devis", "{{devisId}}", "pdf"]}}}]}]}